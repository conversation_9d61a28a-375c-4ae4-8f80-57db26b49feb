"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CustomDomainManager } from '@/components/tenant/custom-domain-manager'
import { SubdomainManager } from '@/components/tenant/subdomain-manager'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Globe, ExternalLink, Settings, Shield, Copy } from 'lucide-react'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseAnonKey)
}

interface TenantData {
  id: string
  name: string
  domain: string | null
  slug: string
}

interface PlatformConfig {
  domain: string
  ip: string
  cdnDomain: string
  apiDomain: string
  ssl: boolean
  wildcardSSL: boolean
  protocol: string
  baseUrl: string
  subdomainPattern: string
  nameservers?: {
    primary: string
    secondary: string
  }
  dnsProvider?: string
  dnsInstructions: {
    aRecord: { name: string; value: string; type: string }
    wildcardRecord: { name: string; value: string; type: string }
    wwwRecord: { name: string; value: string; type: string }
    apiRecord: { name: string; value: string; type: string }
    cdnRecord: { name: string; value: string; type: string }
  }
}

export default function DomainsPage() {
  const [tenantData, setTenantData] = useState<TenantData | null>(null)
  const [platformConfig, setPlatformConfig] = useState<PlatformConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTenantData()
    fetchPlatformConfig()
  }, [])

  const fetchTenantData = async () => {
    try {
      setLoading(true)
      const supabase = getClient()

      // For demo purposes, we'll fetch the first available tenant
      // In production, this would be based on the current user's tenant
      const { data, error: fetchError } = await supabase
        .from('tenants')
        .select('id, name, domain, subdomain as slug')
        .eq('status', 'active')
        .limit(1)
        .single()

      console.log('🔥 TENANT: Fetched tenant data:', data, 'Error:', fetchError)

      if (fetchError) {
        throw fetchError
      }

      setTenantData({
        id: data.id,
        name: data.name,
        domain: data.domain,
        slug: data.subdomain // Use subdomain as slug for consistency
      })

      console.log('🔥 TENANT: Set tenant data:', {
        id: data.id,
        name: data.name,
        domain: data.domain,
        slug: data.subdomain
      })
    } catch (err) {
      console.error('Error fetching tenant data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tenant data')

      // Try to get the actual data from database even if there's an error
      try {
        const supabase = getClient()
        const { data: fallbackData } = await supabase
          .from('tenants')
          .select('id, name, domain, subdomain')
          .eq('id', '00000000-0000-0000-0000-000000000001')
          .single()

        if (fallbackData) {
          setTenantData({
            id: fallbackData.id,
            name: fallbackData.name,
            domain: fallbackData.domain,
            slug: fallbackData.subdomain
          })
        } else {
          // Last resort fallback
          setTenantData({
            id: '00000000-0000-0000-0000-000000000001',
            name: 'Default Tenant',
            domain: null,
            slug: 'dddu'
          })
        }
      } catch (fallbackErr) {
        console.error('Fallback fetch also failed:', fallbackErr)
        // Use hardcoded fallback with correct subdomain
        setTenantData({
          id: '00000000-0000-0000-0000-000000000001',
          name: 'Default Tenant',
          domain: null,
          slug: 'dddu'
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const fetchPlatformConfig = async () => {
    try {
      const response = await fetch('/api/platform/config')
      const data = await response.json()

      if (data.success) {
        setPlatformConfig(data.config)
        console.log('🔥 TENANT: Platform config loaded:', data.config)
      } else {
        console.error('🔥 TENANT: Failed to fetch platform config:', data.error)
      }
    } catch (error) {
      console.error('🔥 TENANT: Error fetching platform config:', error)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-2 text-gray-600">Loading domain settings...</span>
        </div>
      </div>
    )
  }

  if (!tenantData) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Failed to load tenant data</p>
          {error && <p className="text-sm text-gray-500 mt-2">{error}</p>}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Domain Settings</h1>
        <p className="text-muted-foreground">
          Manage your store's domain configuration and custom domain setup
        </p>
      </div>

      {/* Domain Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Domain</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {tenantData.domain ? 'Configured' : 'Not Set'}
            </div>
            <p className="text-xs text-muted-foreground">
              {tenantData.domain || 'No custom domain'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SSL Certificate</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {platformConfig?.ssl ? 'Secured' : 'Not Secured'}
            </div>
            <p className="text-xs text-muted-foreground">
              {platformConfig?.ssl ? 'Auto-managed SSL' : 'SSL not configured'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Subdomain Management */}
      <SubdomainManager
        tenantId={tenantData.id}
        currentSubdomain={tenantData.slug}
        onSubdomainUpdate={fetchTenantData}
      />

      {/* Domain Management */}
      <CustomDomainManager
        tenantId={tenantData.id}
        currentDomain={tenantData.domain}
        onDomainUpdate={fetchTenantData}
      />

      {/* Domain History & Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Domain Configuration Summary</CardTitle>
          <CardDescription>
            Current domain configuration and status overview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Current Configuration */}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Current Subdomain</h4>
                <p className="text-sm text-muted-foreground">
                  {platformConfig ? `${tenantData.slug}.${platformConfig.domain}` : `${tenantData.slug}.sellzio.com`}
                </p>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-green-600">Active & Verified</span>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Custom Domain</h4>
                <p className="text-sm text-muted-foreground">
                  {tenantData.domain || 'Not configured'}
                </p>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${tenantData.domain ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                  <span className={`text-xs ${tenantData.domain ? 'text-blue-600' : 'text-gray-500'}`}>
                    {tenantData.domain ? 'Configured' : 'Not Set'}
                  </span>
                </div>
              </div>
            </div>

            {/* Platform DNS Information */}
            {platformConfig && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-3 flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Platform DNS Configuration
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  <div className="space-y-1">
                    <span className="font-medium text-blue-800">Platform Domain:</span>
                    <p className="text-blue-700 font-mono">{platformConfig.domain}</p>
                  </div>
                  <div className="space-y-1">
                    <span className="font-medium text-blue-800">IP Address:</span>
                    <p className="text-blue-700 font-mono">{platformConfig.ip}</p>
                  </div>
                  <div className="space-y-1">
                    <span className="font-medium text-blue-800">Protocol:</span>
                    <p className="text-blue-700">{platformConfig.protocol.toUpperCase()}</p>
                  </div>
                  {platformConfig.nameservers && (
                    <>
                      <div className="space-y-1">
                        <span className="font-medium text-blue-800">Primary NS:</span>
                        <p className="text-blue-700 font-mono">{platformConfig.nameservers.primary}</p>
                      </div>
                      <div className="space-y-1">
                        <span className="font-medium text-blue-800">Secondary NS:</span>
                        <p className="text-blue-700 font-mono">{platformConfig.nameservers.secondary}</p>
                      </div>
                    </>
                  )}
                  {platformConfig.dnsProvider && (
                    <div className="space-y-1">
                      <span className="font-medium text-blue-800">DNS Provider:</span>
                      <p className="text-blue-700 capitalize">{platformConfig.dnsProvider}</p>
                    </div>
                  )}
                </div>

                {/* DNS Records */}
                <div className="mt-4 pt-4 border-t border-blue-200">
                  <h5 className="font-medium text-blue-900 mb-2">Required DNS Records:</h5>
                  <div className="space-y-2 text-xs">
                    <div className="grid grid-cols-3 gap-2 font-mono bg-blue-100 p-2 rounded">
                      <span className="font-semibold">Type</span>
                      <span className="font-semibold">Name</span>
                      <span className="font-semibold">Value</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 font-mono p-2">
                      <span>A</span>
                      <span>@</span>
                      <span>{platformConfig.ip}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 font-mono p-2 bg-gray-50">
                      <span>A</span>
                      <span>*</span>
                      <span>{platformConfig.ip}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 font-mono p-2">
                      <span>CNAME</span>
                      <span>www</span>
                      <span>{platformConfig.domain}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-3">Quick Actions</h4>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`${platformConfig?.protocol || 'https'}://${tenantData.slug}.${platformConfig?.domain || 'sellzio.com'}`, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Visit Store
                </Button>

                {tenantData.domain && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(`https://${tenantData.domain}`, '_blank')}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Visit Custom Domain
                  </Button>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const text = tenantData.domain || `${tenantData.slug}.${platformConfig?.domain || 'sellzio.com'}`
                    navigator.clipboard.writeText(text)
                  }}
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy Domain
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
